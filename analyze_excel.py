#!/usr/bin/env python3
"""
分析Excel文件结构的脚本
"""

import pandas as pd
import os
import glob

def analyze_excel_files():
    """分析excel文件夹下的所有Excel文件"""
    excel_folder = "excel"
    excel_files = glob.glob(os.path.join(excel_folder, "*.xlsx"))
    
    print(f"找到 {len(excel_files)} 个Excel文件:")
    for file in excel_files:
        print(f"  - {file}")
    
    print("\n" + "="*50)
    
    # 分析每个文件的结构
    for i, excel_file in enumerate(excel_files[:3]):  # 只分析前3个文件
        try:
            print(f"\n分析文件 {i+1}: {excel_file}")
            df = pd.read_excel(excel_file)
            
            print(f"  列名: {list(df.columns)}")
            print(f"  数据形状: {df.shape}")
            print(f"  前3行数据:")
            print(df.head(3).to_string())
            
            # 检查是否有标题列和图片列
            if '标题' in df.columns:
                empty_titles = df['标题'].isna().sum()
                print(f"  空标题行数: {empty_titles}")
            
            if '图片' in df.columns:
                empty_images = df['图片'].isna().sum()
                print(f"  空图片行数: {empty_images}")
                
        except Exception as e:
            print(f"  错误: {e}")
        
        print("-" * 30)

if __name__ == "__main__":
    analyze_excel_files()
